<?php
/**
 * Test script to verify the improved rollback UI refresh fix
 * This script tests the enhanced rollback verification system
 */

// Load WordPress
require_once('../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🧪 Improved Rollback Fix Test</h1>\n";
echo "<p>This test verifies the enhanced rollback system with database verification and retry logic.</p>\n";

// Check if we have any recent fixes to test with
$fix_history = get_option('redco_diagnostic_fix_history', array());

if (empty($fix_history)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>⚠️ No Fix History Available</h3>\n";
    echo "<p>No recent fixes found to test rollback with. Please:</p>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic Module</a></li>\n";
    echo "<li>Run a diagnostic scan</li>\n";
    echo "<li>Apply some fixes</li>\n";
    echo "<li>Return here to test the improved rollback system</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>✅ Fix History Available</h3>\n";
    echo "<p>Found " . count($fix_history) . " fix session(s) in history.</p>\n";
    echo "</div>\n";

    // Display recent fixes for testing
    echo "<h2>📋 Current Recent Fixes</h2>\n";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    
    foreach ($fix_history as $index => $session) {
        $rollback_id = $session['rollback_id'] ?? 'N/A';
        $timestamp = $session['timestamp'] ?? time();
        $details_count = isset($session['details']) ? count($session['details']) : 0;
        
        echo "<div style='border-bottom: 1px solid #dee2e6; padding: 10px 0;'>\n";
        echo "<strong>Session " . ($index + 1) . ":</strong> ";
        echo "Rollback ID: <code>{$rollback_id}</code> | ";
        echo "Time: " . date('Y-m-d H:i:s', $timestamp) . " | ";
        echo "Fixes: {$details_count}\n";
        
        if ($rollback_id !== 'N/A') {
            echo "<br><a href='?test_improved_rollback=" . urlencode($rollback_id) . "' style='background: #007cba; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 12px; margin-top: 5px; display: inline-block;'>🧪 Test Improved Rollback</a>\n";
        }
        echo "</div>\n";
    }
    echo "</div>\n";
}

// Handle improved rollback test
$test_rollback_id = $_GET['test_improved_rollback'] ?? '';
if (!empty($test_rollback_id)) {
    echo "<h2>🧪 Testing Improved Rollback for ID: {$test_rollback_id}</h2>\n";
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🔧 Improved Features</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Database Verification:</strong> Backend verifies the fix is removed before returning success</li>\n";
    echo "<li><strong>Retry Logic:</strong> Frontend retries up to 5 times with exponential backoff (1s, 2s, 4s, 8s)</li>\n";
    echo "<li><strong>Cache Busting:</strong> Enhanced cache invalidation with timestamps</li>\n";
    echo "<li><strong>Automatic Fallback:</strong> Forces page refresh if all retries fail</li>\n";
    echo "</ol>\n";
    echo "</div>\n";

    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🔧 Test Instructions</h3>\n";
    echo "<ol>\n";
    echo "<li>Open the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' target='_blank'>Diagnostic Module</a> in a new tab</li>\n";
    echo "<li>Open your browser's Developer Tools (F12) and go to the Console tab</li>\n";
    echo "<li>Look at the Recent Fixes card in the sidebar</li>\n";
    echo "<li>Click the 'Rollback' button for the fix with ID: <code>{$test_rollback_id}</code></li>\n";
    echo "<li>Watch the console for verification messages</li>\n";
    echo "<li>Observe the improved rollback behavior</li>\n";
    echo "</ol>\n";
    echo "</div>\n";

    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🔍 What to Look For in Console</h3>\n";
    echo "<ul>\n";
    echo "<li><code>🔍 Attempt 1/5: Loading Recent Fixes with verification</code></li>\n";
    echo "<li><code>✅ Verification successful: Rolled-back item removed</code> (if successful)</li>\n";
    echo "<li><code>⚠️ Attempt X: Rolled-back item still present</code> (if retrying)</li>\n";
    echo "<li><code>🔄 Retrying in Xms...</code> (during retry delays)</li>\n";
    echo "<li><code>❌ All verification attempts failed, forcing page refresh</code> (if fallback needed)</li>\n";
    echo "</ul>\n";
    echo "</div>\n";

    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🐛 Debug Information</h3>\n";
    echo "<p><strong>Testing Rollback ID:</strong> <code>{$test_rollback_id}</code></p>\n";
    echo "<p><strong>Backend Improvements:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Added <code>verify_rollback_database_update()</code> method</li>\n";
    echo "<li>Enhanced AJAX response with cache-busting info</li>\n";
    echo "<li>Database verification before returning success</li>\n";
    echo "</ul>\n";
    echo "<p><strong>Frontend Improvements:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Added <code>loadRecentFixesWithVerification()</code> method</li>\n";
    echo "<li>Retry logic with exponential backoff</li>\n";
    echo "<li>Enhanced verification system</li>\n";
    echo "<li>Automatic page refresh fallback</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>📋 Go to Diagnostic Module</a></li>\n";
echo "<li><a href='test-rollback-ui-refresh.php'>🧪 Original Rollback Test</a></li>\n";
echo "<li><a href='check-fix-history.php'>📋 Check Fix History State</a></li>\n";
echo "</ul>\n";

echo "<h2>📝 Improvement Summary</h2>\n";
echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
echo "<p><strong>Problem:</strong> Recent Fixes card still showed rolled-back items due to timing issues between backend database updates and frontend refresh.</p>\n";
echo "<p><strong>Root Cause:</strong> Frontend was refreshing too quickly before database updates completed, and cache invalidation wasn't aggressive enough.</p>\n";
echo "<p><strong>Solution:</strong></p>\n";
echo "<ol>\n";
echo "<li><strong>Backend Verification:</strong> Added database verification to ensure rollback completed before returning success</li>\n";
echo "<li><strong>Retry Logic:</strong> Frontend now retries up to 5 times with exponential backoff if item still present</li>\n";
echo "<li><strong>Enhanced Cache Busting:</strong> Improved cache invalidation with timestamps and debug info</li>\n";
echo "<li><strong>Automatic Fallback:</strong> Forces page refresh if all verification attempts fail</li>\n";
echo "</ol>\n";
echo "<p><strong>Expected Result:</strong> Immediate and reliable removal of rolled-back items from Recent Fixes list.</p>\n";
echo "</div>\n";
?>
