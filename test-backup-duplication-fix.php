<?php
/**
 * Test script to verify that duplicate backup creation has been eliminated
 * 
 * This script tests the fix for the duplicate backup creation issue in the
 * Diagnostic & Auto-Fix module.
 */

// Load WordPress
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== TESTING BACKUP DUPLICATION FIX ===\n";
echo "Testing that only ONE backup is created per fix operation\n\n";

// Include required files
if (file_exists('includes/helpers.php')) {
    require_once('includes/helpers.php');
} else {
    echo "Warning: helpers.php not found, using direct path\n";
}

require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

// Get unified backup directory
if (function_exists('redco_get_unified_backup_dir')) {
    $backup_dir = redco_get_unified_backup_dir();
} else {
    // Fallback to direct path
    $upload_dir = wp_upload_dir();
    $backup_dir = $upload_dir['basedir'] . '/redco-backups/';
    if (!file_exists($backup_dir)) {
        wp_mkdir_p($backup_dir);
    }
}
echo "Unified backup directory: {$backup_dir}\n";

// Count existing backups before test
$existing_backups = array();
if (is_dir($backup_dir)) {
    $existing_backups = glob($backup_dir . 'backup_*');
}
$initial_backup_count = count($existing_backups);
echo "Initial backup count: {$initial_backup_count}\n";

// Count existing .htaccess backup files in document root
$htaccess_backups = glob(ABSPATH . '.htaccess.redco-backup-*');
$initial_htaccess_backup_count = count($htaccess_backups);
echo "Initial .htaccess backup files: {$initial_htaccess_backup_count}\n\n";

// Create test issue for compression fix
$test_issue = array(
    'id' => 'test_compression_backup',
    'title' => 'Test Compression Fix for Backup Verification',
    'fix_action' => 'enable_compression',
    'auto_fixable' => true,
    'category' => 'performance'
);

echo "=== APPLYING TEST FIX ===\n";
echo "Creating AutoFix Engine...\n";
$engine = new Redco_Diagnostic_AutoFix_Engine();

echo "Applying compression fix...\n";
$result = $engine->apply_fix($test_issue);

echo "Fix result:\n";
echo "  Success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
echo "  Message: " . $result['message'] . "\n";
if (isset($result['rollback_id'])) {
    echo "  Rollback ID: " . $result['rollback_id'] . "\n";
}

echo "\n=== BACKUP VERIFICATION ===\n";

// Count backups after fix
$new_backups = array();
if (is_dir($backup_dir)) {
    $new_backups = glob($backup_dir . 'backup_*');
}
$final_backup_count = count($new_backups);
$backup_increase = $final_backup_count - $initial_backup_count;

echo "Final backup count: {$final_backup_count}\n";
echo "Backup increase: {$backup_increase}\n";

// Count .htaccess backup files after fix
$final_htaccess_backups = glob(ABSPATH . '.htaccess.redco-backup-*');
$final_htaccess_backup_count = count($final_htaccess_backups);
$htaccess_backup_increase = $final_htaccess_backup_count - $initial_htaccess_backup_count;

echo "Final .htaccess backup files: {$final_htaccess_backup_count}\n";
echo "Individual .htaccess backup increase: {$htaccess_backup_increase}\n\n";

// Verify results
echo "=== VERIFICATION RESULTS ===\n";

if ($backup_increase === 1) {
    echo "✅ PASS: Exactly ONE engine backup was created\n";
} elseif ($backup_increase === 0) {
    echo "⚠️  WARNING: No engine backup was created (may be expected if fix was skipped)\n";
} else {
    echo "❌ FAIL: Multiple engine backups were created ({$backup_increase})\n";
}

if ($htaccess_backup_increase === 0) {
    echo "✅ PASS: No individual .htaccess backup files were created\n";
} else {
    echo "❌ FAIL: Individual .htaccess backup files were created ({$htaccess_backup_increase})\n";
}

// Overall assessment
$duplicate_issue_fixed = ($htaccess_backup_increase === 0);
echo "\n=== OVERALL ASSESSMENT ===\n";
if ($duplicate_issue_fixed) {
    echo "✅ SUCCESS: Duplicate backup creation issue has been FIXED!\n";
    echo "   - Engine creates comprehensive backups\n";
    echo "   - Individual file backups are disabled\n";
    echo "   - No duplicate backup files created\n";
} else {
    echo "❌ FAILURE: Duplicate backup creation issue still exists\n";
    echo "   - Individual .htaccess backup files are still being created\n";
    echo "   - This indicates the fix was not successful\n";
}

echo "\n=== CLEANUP VERIFICATION ===\n";
if ($final_htaccess_backup_count > 0) {
    echo "Found {$final_htaccess_backup_count} individual .htaccess backup files:\n";
    foreach ($final_htaccess_backups as $backup_file) {
        echo "  - " . basename($backup_file) . "\n";
    }
    echo "\nThese files should be cleaned up as they are no longer needed.\n";
} else {
    echo "✅ No individual .htaccess backup files found - system is clean\n";
}

echo "\n=== TEST COMPLETE ===\n";
