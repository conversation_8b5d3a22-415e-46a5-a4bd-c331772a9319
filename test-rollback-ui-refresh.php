<?php
/**
 * Test script to verify rollback UI refresh behavior
 * This script tests the Recent Fixes list refresh after rollback operations
 */

// Load WordPress
require_once('../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🧪 Rollback UI Refresh Test</h1>\n";
echo "<p>This test verifies that the Recent Fixes list properly refreshes after rollback operations.</p>\n";

// Check if we have any recent fixes to test with
$fix_history = get_option('redco_diagnostic_fix_history', array());

if (empty($fix_history)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>⚠️ No Fix History Available</h3>\n";
    echo "<p>No recent fixes found to test rollback with. Please:</p>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic Module</a></li>\n";
    echo "<li>Run a diagnostic scan</li>\n";
    echo "<li>Apply some fixes</li>\n";
    echo "<li>Return here to test rollback UI refresh</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>✅ Fix History Available</h3>\n";
    echo "<p>Found " . count($fix_history) . " fix session(s) in history.</p>\n";
    echo "</div>\n";

    // Display recent fixes for testing
    echo "<h2>📋 Current Recent Fixes</h2>\n";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    
    foreach ($fix_history as $index => $session) {
        $rollback_id = $session['rollback_id'] ?? 'N/A';
        $timestamp = $session['timestamp'] ?? time();
        $details_count = isset($session['details']) ? count($session['details']) : 0;
        
        echo "<div style='border-bottom: 1px solid #dee2e6; padding: 10px 0;'>\n";
        echo "<strong>Session " . ($index + 1) . ":</strong> ";
        echo "Rollback ID: <code>{$rollback_id}</code> | ";
        echo "Time: " . date('Y-m-d H:i:s', $timestamp) . " | ";
        echo "Fixes: {$details_count}\n";
        
        if ($rollback_id !== 'N/A') {
            echo "<br><a href='?test_rollback=" . urlencode($rollback_id) . "' style='background: #dc3232; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; font-size: 12px; margin-top: 5px; display: inline-block;'>🧪 Test Rollback UI Refresh</a>\n";
        }
        echo "</div>\n";
    }
    echo "</div>\n";
}

// Handle rollback test
$test_rollback_id = $_GET['test_rollback'] ?? '';
if (!empty($test_rollback_id)) {
    echo "<h2>🧪 Testing Rollback UI Refresh for ID: {$test_rollback_id}</h2>\n";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🔧 Test Instructions</h3>\n";
    echo "<ol>\n";
    echo "<li>Open the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' target='_blank'>Diagnostic Module</a> in a new tab</li>\n";
    echo "<li>Look at the Recent Fixes card in the sidebar</li>\n";
    echo "<li>Click the 'Rollback' button for the fix with ID: <code>{$test_rollback_id}</code></li>\n";
    echo "<li>Observe if the Recent Fixes list refreshes immediately after rollback</li>\n";
    echo "<li>The rolled-back item should disappear from the Recent Fixes list</li>\n";
    echo "</ol>\n";
    echo "</div>\n";

    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🔍 What to Look For</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>✅ Expected Behavior:</strong> The Recent Fixes list should refresh immediately and the rolled-back item should disappear</li>\n";
    echo "<li><strong>❌ Bug Behavior:</strong> The Recent Fixes list still shows the rolled-back item even after rollback completes</li>\n";
    echo "<li><strong>🔄 Fallback Behavior:</strong> If the item persists, the page should automatically refresh after 2 seconds</li>\n";
    echo "</ul>\n";
    echo "</div>\n";

    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>🐛 Debug Information</h3>\n";
    echo "<p><strong>Testing Rollback ID:</strong> <code>{$test_rollback_id}</code></p>\n";
    echo "<p><strong>JavaScript Changes Made:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Replaced <code>clearCache('redco_recent_fixes')</code> with <code>clearAllCaches()</code></li>\n";
    echo "<li>Added aggressive cache invalidation with timestamp</li>\n";
    echo "<li>Added immediate verification after rollback</li>\n";
    echo "<li>Added automatic page refresh fallback if item persists</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>📋 Go to Diagnostic Module</a></li>\n";
echo "<li><a href='check-fix-history.php'>📋 Check Fix History State</a></li>\n";
echo "<li><a href='debug-rollback-process.php'>🐛 Debug Rollback Process</a></li>\n";
echo "</ul>\n";

echo "<h2>📝 Test Summary</h2>\n";
echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
echo "<p><strong>Issue:</strong> Recent Fixes card still shows rolled-back items even after successful rollback.</p>\n";
echo "<p><strong>Root Cause:</strong> JavaScript cache was not being aggressively cleared, causing stale data to persist.</p>\n";
echo "<p><strong>Fix Applied:</strong> Replaced selective cache clearing with aggressive cache clearing including timestamp invalidation.</p>\n";
echo "<p><strong>Fallback:</strong> Added automatic page refresh if the UI doesn't update properly.</p>\n";
echo "</div>\n";
?>
