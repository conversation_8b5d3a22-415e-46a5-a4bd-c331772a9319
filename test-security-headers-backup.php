<?php
/**
 * Test script to verify backup creation for security headers fix
 */

// Load WordPress
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== TESTING SECURITY HEADERS BACKUP ===\n";

// Include required files
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

// Get backup directory
$upload_dir = wp_upload_dir();
$backup_dir = $upload_dir['basedir'] . '/redco-backups/';

// Count existing backups
$existing_backups = is_dir($backup_dir) ? glob($backup_dir . 'backup_*') : array();
$initial_count = count($existing_backups);

// Count existing .htaccess backup files
$htaccess_backups = glob(ABSPATH . '.htaccess.redco-backup-*');
$initial_htaccess_count = count($htaccess_backups);

echo "Initial engine backups: {$initial_count}\n";
echo "Initial .htaccess backups: {$initial_htaccess_count}\n\n";

// Create test issue for security headers
$test_issue = array(
    'id' => 'missing_security_header_X-Content-Type-Options',
    'title' => 'Missing Security Header: X-Content-Type-Options',
    'fix_action' => 'add_security_headers',
    'auto_fixable' => true,
    'category' => 'security'
);

echo "Applying security headers fix...\n";
$engine = new Redco_Diagnostic_AutoFix_Engine();
$result = $engine->apply_fix($test_issue);

echo "Fix result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $result['message'] . "\n\n";

// Count backups after fix
$final_backups = is_dir($backup_dir) ? glob($backup_dir . 'backup_*') : array();
$final_count = count($final_backups);

$final_htaccess_backups = glob(ABSPATH . '.htaccess.redco-backup-*');
$final_htaccess_count = count($final_htaccess_backups);

echo "Final engine backups: {$final_count}\n";
echo "Final .htaccess backups: {$final_htaccess_count}\n\n";

$engine_increase = $final_count - $initial_count;
$htaccess_increase = $final_htaccess_count - $initial_htaccess_count;

echo "=== RESULTS ===\n";
echo "Engine backup increase: {$engine_increase}\n";
echo "Individual .htaccess backup increase: {$htaccess_increase}\n\n";

if ($engine_increase === 1 && $htaccess_increase === 0) {
    echo "✅ SUCCESS: Only ONE backup created (engine backup)\n";
    echo "✅ No duplicate individual .htaccess backups\n";
} else {
    echo "❌ ISSUE: Unexpected backup behavior\n";
    if ($engine_increase !== 1) {
        echo "   - Engine backup count: {$engine_increase} (expected: 1)\n";
    }
    if ($htaccess_increase !== 0) {
        echo "   - Individual backups created: {$htaccess_increase} (expected: 0)\n";
    }
}
