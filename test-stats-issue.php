<?php
/**
 * Test the statistics issue specifically
 */

// Load WordPress
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== TESTING STATISTICS ISSUE ===\n";

// Check current stats
$current_stats = get_option('redco_diagnostic_stats', array());
echo "Current stats: " . json_encode($current_stats, JSON_PRETTY_PRINT) . "\n";

// Initialize stats if empty
if (empty($current_stats)) {
    echo "Stats are empty, initializing...\n";
    $current_stats = array(
        'health_score' => 75,
        'performance_score' => 70,
        'issues_found' => 0,
        'critical_issues' => 0,
        'auto_fixable_issues' => 0,
        'fixes_applied' => 0,
        'last_scan' => time(),
        'last_updated' => time()
    );
    update_option('redco_diagnostic_stats', $current_stats);
    echo "Stats initialized\n";
}

// Set a test value for fixes_applied
$current_stats['fixes_applied'] = 5;
update_option('redco_diagnostic_stats', $current_stats);
echo "Set fixes_applied to 5\n";

// Test the rollback statistics update
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
$engine = new Redco_Diagnostic_AutoFix_Engine();

// Use reflection to call the private method
$reflection = new ReflectionClass($engine);
$method = $reflection->getMethod('update_statistics_after_rollback');
$method->setAccessible(true);

$rollback_info = array(
    'issues_cleared' => 2,
    'sessions_removed' => 1
);

echo "Calling update_statistics_after_rollback with info: " . json_encode($rollback_info) . "\n";

$result = $method->invoke($engine, $rollback_info);

echo "Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";

// Check stats after update
$updated_stats = get_option('redco_diagnostic_stats', array());
echo "Updated stats: " . json_encode($updated_stats, JSON_PRETTY_PRINT) . "\n";

echo "=== TEST COMPLETE ===\n";
