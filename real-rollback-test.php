<?php
/**
 * REAL rollback test - actually test what happens
 */

// Load WordPress
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== REAL ROLLBACK TEST ===\n";

// Include required files
require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

$engine = new Redco_Diagnostic_AutoFix_Engine();

echo "=== STEP 1: CREATE A REAL FIX ===\n";

// Clean .htaccess first
$htaccess_file = ABSPATH . '.htaccess';
if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $content = preg_replace('/# Enable GZIP Compression.*?# End GZIP Compression\s*/s', '', $content);
    file_put_contents($htaccess_file, $content);
    echo "✅ Cleaned .htaccess\n";
}

// Check initial .htaccess state
$initial_htaccess = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';
$initial_has_compression = strpos($initial_htaccess, 'mod_deflate') !== false;
echo "Initial .htaccess compression: " . ($initial_has_compression ? 'YES' : 'NO') . "\n";

// Create a compression fix
$test_issue = array(
    'id' => 'real_compression_test',
    'title' => 'Enable GZIP Compression',
    'fix_action' => 'enable_compression',
    'auto_fixable' => true,
    'category' => 'performance'
);

echo "Applying compression fix...\n";
$fix_result = $engine->apply_single_fix_with_backup($test_issue);

if (!$fix_result['success']) {
    echo "❌ Fix failed: " . $fix_result['message'] . "\n";
    exit(1);
}

$backup_id = $fix_result['rollback_id'];
echo "✅ Fix applied successfully\n";
echo "   Backup ID: {$backup_id}\n";

// Check .htaccess after fix
$after_fix_htaccess = file_get_contents($htaccess_file);
$after_fix_has_compression = strpos($after_fix_htaccess, 'mod_deflate') !== false;
echo "After fix .htaccess compression: " . ($after_fix_has_compression ? 'YES' : 'NO') . "\n";

// Record in fix history
$fix_session = array(
    'timestamp' => time(),
    'fixes_applied' => 1,
    'fixes_failed' => 0,
    'backup_created' => true,
    'rollback_id' => $backup_id,
    'details' => array(
        array(
            'issue_id' => $test_issue['id'],
            'issue_title' => $test_issue['title'],
            'success' => true,
            'message' => 'Real test fix applied',
            'rollback_id' => $backup_id
        )
    )
);

$fix_history = get_option('redco_diagnostic_fix_history', array());
$fix_history[] = $fix_session;
update_option('redco_diagnostic_fix_history', $fix_history);

$fixed_issues = get_option('redco_fixed_issues', array());
$fixed_issues[$test_issue['id']] = array(
    'timestamp' => time(),
    'rollback_id' => $backup_id
);
update_option('redco_fixed_issues', $fixed_issues);

echo "\n=== STEP 2: VERIFY INITIAL STATE ===\n";

$initial_fix_history = get_option('redco_diagnostic_fix_history', array());
$initial_fixed_issues = get_option('redco_fixed_issues', array());
$initial_stats = get_option('redco_diagnostic_stats', array());

echo "Fix history sessions: " . count($initial_fix_history) . "\n";
echo "Fixed issues: " . count($initial_fixed_issues) . "\n";
echo "Stats fixes_applied: " . ($initial_stats['fixes_applied'] ?? 'N/A') . "\n";

// Check backup exists
$upload_dir = wp_upload_dir();
$backup_dir = $upload_dir['basedir'] . '/redco-backups/' . $backup_id;
$backup_exists = is_dir($backup_dir);
echo "Backup directory exists: " . ($backup_exists ? 'YES' : 'NO') . "\n";

if ($backup_exists) {
    $backup_files = scandir($backup_dir);
    $backup_files = array_diff($backup_files, array('.', '..'));
    echo "Backup files: " . implode(', ', $backup_files) . "\n";
}

echo "\n=== STEP 3: EXECUTE ROLLBACK ===\n";

echo "Rolling back fix with backup ID: {$backup_id}\n";

$rollback_result = $engine->rollback_fixes($backup_id);

echo "Rollback result:\n";
echo "  Success: " . ($rollback_result['success'] ? 'YES' : 'NO') . "\n";
echo "  Files restored: " . ($rollback_result['files_restored'] ?? 0) . "\n";
echo "  Options restored: " . ($rollback_result['options_restored'] ?? 0) . "\n";

if (!empty($rollback_result['errors'])) {
    echo "  Errors: " . implode(', ', $rollback_result['errors']) . "\n";
}

echo "\n=== STEP 4: VERIFY ROLLBACK RESULTS ===\n";

// Check .htaccess after rollback
if (file_exists($htaccess_file)) {
    $after_rollback_htaccess = file_get_contents($htaccess_file);
    $after_rollback_has_compression = strpos($after_rollback_htaccess, 'mod_deflate') !== false;
    echo "After rollback .htaccess compression: " . ($after_rollback_has_compression ? 'YES' : 'NO') . "\n";
    
    $compression_reverted = $after_fix_has_compression && !$after_rollback_has_compression;
    echo "Compression rules reverted: " . ($compression_reverted ? 'YES ✅' : 'NO ❌') . "\n";
} else {
    echo ".htaccess file: NOT FOUND\n";
}

// Check database changes
$final_fix_history = get_option('redco_diagnostic_fix_history', array());
$final_fixed_issues = get_option('redco_fixed_issues', array());
$final_stats = get_option('redco_diagnostic_stats', array());

$history_change = count($initial_fix_history) - count($final_fix_history);
$issues_change = count($initial_fixed_issues) - count($final_fixed_issues);

echo "Fix history change: {$history_change} sessions removed\n";
echo "Fixed issues change: {$issues_change} issues removed\n";

$stats_before = $initial_stats['fixes_applied'] ?? 0;
$stats_after = $final_stats['fixes_applied'] ?? 0;
$stats_change = $stats_before - $stats_after;
echo "Statistics change: fixes_applied {$stats_before} → {$stats_after} (change: {$stats_change})\n";

$has_rollback_timestamp = isset($final_stats['last_rollback']);
echo "Rollback timestamp set: " . ($has_rollback_timestamp ? 'YES ✅' : 'NO ❌') . "\n";

// Check backup cleanup
$backup_cleaned = !is_dir($backup_dir);
echo "Backup directory cleaned: " . ($backup_cleaned ? 'YES ✅' : 'NO ❌') . "\n";

echo "\n=== STEP 5: FINAL ASSESSMENT ===\n";

$rollback_components = array(
    'File Restoration (.htaccess)' => isset($compression_reverted) && $compression_reverted,
    'Database Cleanup (History)' => $history_change > 0,
    'Database Cleanup (Issues)' => $issues_change > 0,
    'Statistics Update' => $has_rollback_timestamp && $stats_change > 0,
    'Backup Cleanup' => $backup_cleaned
);

echo "ROLLBACK COMPONENT ASSESSMENT:\n";
$working_count = 0;
foreach ($rollback_components as $component => $working) {
    echo ($working ? "✅" : "❌") . " {$component}: " . ($working ? "WORKING" : "NOT WORKING") . "\n";
    if ($working) $working_count++;
}

$total_count = count($rollback_components);
echo "\nOVERALL SCORE: {$working_count}/{$total_count} components working\n";

if ($working_count === $total_count) {
    echo "🎉 ROLLBACK IS FULLY FUNCTIONAL!\n";
} elseif ($working_count >= 3) {
    echo "⚠️ ROLLBACK IS PARTIALLY FUNCTIONAL\n";
} else {
    echo "❌ ROLLBACK IS BROKEN\n";
}

echo "\n=== SPECIFIC ISSUES TO FIX ===\n";

if (!isset($compression_reverted) || !$compression_reverted) {
    echo "❌ CRITICAL: .htaccess file is not being restored properly\n";
}

if ($history_change <= 0) {
    echo "❌ CRITICAL: Fix history is not being cleaned up\n";
}

if ($issues_change <= 0) {
    echo "❌ CRITICAL: Fixed issues are not being removed\n";
}

if (!$has_rollback_timestamp || $stats_change <= 0) {
    echo "❌ CRITICAL: Statistics are not being updated\n";
}

if (!$backup_cleaned) {
    echo "❌ ISSUE: Backup files are not being cleaned up\n";
}

echo "\n=== TEST COMPLETE ===\n";
