<?php
/**
 * Test the final backup fix
 */

// Load WordPress
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== TESTING FINAL BACKUP FIX ===\n";

// Include required files
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

// Get backup directory
$upload_dir = wp_upload_dir();
$backup_dir = $upload_dir['basedir'] . '/redco-backups/';

// Count existing backups
$existing_backups = is_dir($backup_dir) ? glob($backup_dir . 'backup_*') : array();
$initial_count = count($existing_backups);

echo "Initial backup count: {$initial_count}\n";

// Remove compression rules to test actual changes
$htaccess_file = ABSPATH . '.htaccess';
if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $content = preg_replace('/# Enable GZIP Compression.*?# End GZIP Compression\s*/s', '', $content);
    file_put_contents($htaccess_file, $content);
    echo "Removed existing compression rules\n";
}

// Create test issue
$test_issue = array(
    'id' => 'test_single_fix_backup',
    'title' => 'Test Single Fix With Backup',
    'fix_action' => 'enable_compression',
    'auto_fixable' => true,
    'category' => 'performance'
);

echo "\n=== TESTING SINGLE FIX (SHOULD CREATE 1 BACKUP) ===\n";
$engine = new Redco_Diagnostic_AutoFix_Engine();
$result = $engine->apply_single_fix_with_backup($test_issue);

echo "Fix result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $result['message'] . "\n";
echo "Rollback ID: " . ($result['rollback_id'] ?? 'NONE') . "\n";

// Count backups after single fix
$after_single_backups = is_dir($backup_dir) ? glob($backup_dir . 'backup_*') : array();
$after_single_count = count($after_single_backups);
$single_backup_increase = $after_single_count - $initial_count;

echo "Backup increase from single fix: {$single_backup_increase}\n";

if ($single_backup_increase === 1) {
    echo "✅ SUCCESS: Exactly 1 backup created for single fix\n";
} else {
    echo "❌ ISSUE: Expected 1 backup, got {$single_backup_increase}\n";
}

echo "\n=== TESTING BULK FIX (SHOULD CREATE 1 MORE BACKUP) ===\n";

// Remove compression rules again
if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $content = preg_replace('/# Enable GZIP Compression.*?# End GZIP Compression\s*/s', '', $content);
    file_put_contents($htaccess_file, $content);
}

// Test bulk fix
$bulk_result = $engine->apply_auto_fixes(array($test_issue), true);

echo "Bulk fix result: " . ($bulk_result['fixes_applied'] > 0 ? 'SUCCESS' : 'FAILED') . "\n";
echo "Backup created: " . ($bulk_result['backup_created'] ? 'YES' : 'NO') . "\n";
echo "Rollback ID: " . ($bulk_result['rollback_id'] ?? 'NONE') . "\n";

// Count backups after bulk fix
$after_bulk_backups = is_dir($backup_dir) ? glob($backup_dir . 'backup_*') : array();
$after_bulk_count = count($after_bulk_backups);
$bulk_backup_increase = $after_bulk_count - $after_single_count;

echo "Backup increase from bulk fix: {$bulk_backup_increase}\n";

if ($bulk_backup_increase === 1) {
    echo "✅ SUCCESS: Exactly 1 backup created for bulk fix\n";
} elseif ($bulk_backup_increase === 0) {
    echo "✅ OK: No backup created (rules already exist)\n";
} else {
    echo "❌ ISSUE: Expected 0-1 backup, got {$bulk_backup_increase}\n";
}

echo "\n=== TESTING SUBSEQUENT SINGLE FIX (SHOULD CREATE 1 MORE BACKUP) ===\n";

// Remove compression rules again
if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $content = preg_replace('/# Enable GZIP Compression.*?# End GZIP Compression\s*/s', '', $content);
    file_put_contents($htaccess_file, $content);
}

$second_single_result = $engine->apply_single_fix_with_backup($test_issue);

echo "Second single fix result: " . ($second_single_result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Rollback ID: " . ($second_single_result['rollback_id'] ?? 'NONE') . "\n";

// Count backups after second single fix
$final_backups = is_dir($backup_dir) ? glob($backup_dir . 'backup_*') : array();
$final_count = count($final_backups);
$second_single_backup_increase = $final_count - $after_bulk_count;

echo "Backup increase from second single fix: {$second_single_backup_increase}\n";

$total_backup_increase = $final_count - $initial_count;

echo "\n=== FINAL ASSESSMENT ===\n";
echo "Total backup increase: {$total_backup_increase}\n";
echo "Expected: 3 backups (1 single + 1 bulk + 1 single)\n";

if ($total_backup_increase === 3) {
    echo "🎉 PERFECT: Each operation created exactly 1 backup\n";
    echo "   - Single fixes create their own backups\n";
    echo "   - Bulk fixes create their own backups\n";
    echo "   - No duplicate backup creation\n";
} else {
    echo "❌ ISSUE: Expected 3 total backups, got {$total_backup_increase}\n";
}

echo "\n=== TEST COMPLETE ===\n";
